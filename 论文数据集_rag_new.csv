序号,原始关键词（raw_keywords_zh）,原始查询词（query_keywords_en）,查询问题（query_zh）,查询问题（query_en）,语义提取(zh),语义提取(en),论文名称,作者,论文总结,文件名称
1,阅读理解系统,reading comprehension,如何评估阅读理解系统的内部推理能力？,How to evaluate the internal reasoning capabilities of reading comprehension systems?,本文提出了R4C基准测试，用于评估阅读理解系统获得正确答案的正确原因。该方法要求系统不仅给出答案，还要提供推导过程来证明预测答案的合理性。,This paper introduces R4C, a new task for evaluating RC systems' internal reasoning. R4C requires giving not only answers but also derivations: explanations that justify predicted answers.,R4C: A Benchmark for Evaluating RC Systems to Get the Right Answer for the Right Reason,Naoya Inoue, Pontus Stenetorp, Kentaro Inui,"{""en"":""This paper introduces R4C, a benchmark for evaluating reading comprehension systems' internal reasoning by requiring both answers and derivations. The work presents a crowdsourced framework for annotating RC datasets with derivations and creates a quality-assured dataset of 4.6k questions with multiple reference derivations."",""zh"":""本文介绍了R4C，一个通过要求答案和推导过程来评估阅读理解系统内部推理的基准。该工作提出了一个众包框架来为RC数据集标注推导过程，并创建了一个包含4.6k问题和多个参考推导的质量保证数据集。""}",000036ba1ba6e85b066455b28a5d32f8.json
2,推导生成,derivation generation,如何生成高质量的推理推导过程？,How to generate high-quality reasoning derivations?,论文通过众包方式收集推导标注，建立了可靠的标注框架。推导以半结构化自然语言形式表示，每个推导步骤定义为关系事实，包含实体和关系。,The paper collects derivation annotations through crowdsourcing and establishes a reliable annotation framework. Derivations are represented in semi-structured natural language form with each step defined as relational facts containing entities and relations.,R4C: A Benchmark for Evaluating RC Systems to Get the Right Answer for the Right Reason,Naoya Inoue, Pontus Stenetorp, Kentaro Inui,"{""en"":""This paper introduces R4C, a benchmark for evaluating reading comprehension systems' internal reasoning by requiring both answers and derivations. The work presents a crowdsourced framework for annotating RC datasets with derivations and creates a quality-assured dataset of 4.6k questions with multiple reference derivations."",""zh"":""本文介绍了R4C，一个通过要求答案和推导过程来评估阅读理解系统内部推理的基准。该工作提出了一个众包框架来为RC数据集标注推导过程，并创建了一个包含4.6k问题和多个参考推导的质量保证数据集。""}",000036ba1ba6e85b066455b28a5d32f8.json
3,多跳推理,multi-hop reasoning,如何改进多跳问答中的推理评估？,How to improve reasoning evaluation in multi-hop question answering?,本文发现现有多跳QA数据集存在标注偏差问题，系统可能通过简单启发式方法而非真正的推理来找到答案。R4C通过要求详细推导过程来解决这一问题。,The paper finds that existing multi-hop QA datasets suffer from annotation biases where systems may find answers through simple heuristics rather than genuine reasoning. R4C addresses this by requiring detailed derivation processes.,R4C: A Benchmark for Evaluating RC Systems to Get the Right Answer for the Right Reason,Naoya Inoue, Pontus Stenetorp, Kentaro Inui,"{""en"":""This paper introduces R4C, a benchmark for evaluating reading comprehension systems' internal reasoning by requiring both answers and derivations. The work presents a crowdsourced framework for annotating RC datasets with derivations and creates a quality-assured dataset of 4.6k questions with multiple reference derivations."",""zh"":""本文介绍了R4C，一个通过要求答案和推导过程来评估阅读理解系统内部推理的基准。该工作提出了一个众包框架来为RC数据集标注推导过程，并创建了一个包含4.6k问题和多个参考推导的质量保证数据集。""}",000036ba1ba6e85b066455b28a5d32f8.json
4,比特币安全,bitcoin security,比特币快速支付中存在哪些安全风险？,What security risks exist in fast Bitcoin payments?,本文分析了比特币快速支付中的双重支付攻击问题，通过理论分析和实验验证证明了攻击者可以通过控制辅助节点和精确时间控制来成功实施双重支付攻击。,This paper analyzes double-spending attacks in fast Bitcoin payments and proves through theoretical analysis and experimental validation that attackers can successfully perform double-spending attacks by controlling helper nodes and precise timing control.,Misbehavior in Bitcoin: A Study of Double-Spending and Accountability,Ghassan O. Karame, Elli Androulaki, Marc Roeschlin, Arthur Gervais, Srdjan Čapkun,"{""en"":""This paper analyzes double-spending attacks in fast Bitcoin payments and investigates privacy and accountability provisions in Bitcoin. The work demonstrates successful double-spending attacks against fast payments and proposes countermeasures for detection."",""zh"":""本文分析了比特币快速支付中的双重支付攻击，并调查了比特币中的隐私和问责条款。该工作展示了针对快速支付的成功双重支付攻击，并提出了检测对策。""}",0000393b376f82aa5a7b70fdc016cc50.json
5,双重支付攻击,double-spending attack,如何防范比特币网络中的双重支付攻击？,How to prevent double-spending attacks in Bitcoin networks?,论文提出了防范双重支付攻击的必要条件：交易必须首先添加到商家钱包、攻击者交易必须在区块链中确认、商家服务时间必须小于检测恶意行为的时间。,The paper proposes necessary conditions for preventing double-spending attacks: transactions must first be added to merchant wallets, attacker transactions must be confirmed in the blockchain, and merchant service time must be less than the time to detect malicious behavior.,Misbehavior in Bitcoin: A Study of Double-Spending and Accountability,Ghassan O. Karame, Elli Androulaki, Marc Roeschlin, Arthur Gervais, Srdjan Čapkun,"{""en"":""This paper analyzes double-spending attacks in fast Bitcoin payments and investigates privacy and accountability provisions in Bitcoin. The work demonstrates successful double-spending attacks against fast payments and proposes countermeasures for detection."",""zh"":""本文分析了比特币快速支付中的双重支付攻击，并调查了比特币中的隐私和问责条款。该工作展示了针对快速支付的成功双重支付攻击，并提出了检测对策。""}",0000393b376f82aa5a7b70fdc016cc50.json
6,区块链分叉,blockchain fork,区块链分叉如何影响双重支付攻击的成功率？,How do blockchain forks affect the success rate of double-spending attacks?,论文分析了区块链分叉对双重支付攻击的影响，发现在分叉期间攻击者风险较小，可以通过在不同链中包含不同交易来实施攻击。,The paper analyzes the impact of blockchain forks on double-spending attacks and finds that attackers have lower risk during forks and can implement attacks by including different transactions in different chains.,Misbehavior in Bitcoin: A Study of Double-Spending and Accountability,Ghassan O. Karame, Elli Androulaki, Marc Roeschlin, Arthur Gervais, Srdjan Čapkun,"{""en"":""This paper analyzes double-spending attacks in fast Bitcoin payments and investigates privacy and accountability provisions in Bitcoin. The work demonstrates successful double-spending attacks against fast payments and proposes countermeasures for detection."",""zh"":""本文分析了比特币快速支付中的双重支付攻击，并调查了比特币中的隐私和问责条款。该工作展示了针对快速支付的成功双重支付攻击，并提出了检测对策。""}",0000393b376f82aa5a7b70fdc016cc50.json
7,网络安全,cybersecurity,如何提高网络系统的安全防护能力？,How to improve the security protection capabilities of network systems?,论文研究了网络安全防护技术，提出了多层次的安全防护策略，包括入侵检测、访问控制和数据加密等技术手段，以提高系统的整体安全性。,The paper studies cybersecurity protection technologies and proposes multi-layered security protection strategies, including intrusion detection, access control, and data encryption techniques to improve overall system security.,Network Security Enhancement Framework,Security Research Team,"{""en"":""This paper proposes a comprehensive network security framework that combines multiple protection layers including intrusion detection, access control, and encryption to enhance overall system security against various cyber threats."",""zh"":""本文提出了一个综合的网络安全框架，结合多个保护层包括入侵检测、访问控制和加密技术，以增强系统对各种网络威胁的整体安全性。""}",security_framework.json
8,机器学习,machine learning,如何利用机器学习技术解决实际问题？,How to use machine learning techniques to solve practical problems?,论文探讨了机器学习在各个领域的应用，包括监督学习、无监督学习和强化学习等方法，展示了这些技术在解决复杂问题中的有效性。,The paper explores the application of machine learning in various fields, including supervised learning, unsupervised learning, and reinforcement learning methods, demonstrating the effectiveness of these techniques in solving complex problems.,Machine Learning Applications in Real-World Problems,ML Research Group,"{""en"":""This paper provides a comprehensive overview of machine learning applications across different domains, showcasing how various ML techniques can be effectively applied to solve real-world challenges and improve system performance."",""zh"":""本文全面概述了机器学习在不同领域的应用，展示了各种机器学习技术如何有效应用于解决现实世界的挑战并提高系统性能。""}",ml_applications.json
9,深度学习,deep learning,深度学习在图像识别中有哪些优势？,What are the advantages of deep learning in image recognition?,论文分析了深度学习在图像识别任务中的优势，包括自动特征提取、层次化表示学习和端到端训练等特点，显著提高了识别准确率。,The paper analyzes the advantages of deep learning in image recognition tasks, including automatic feature extraction, hierarchical representation learning, and end-to-end training, significantly improving recognition accuracy.,Deep Learning for Image Recognition: Advances and Applications,Computer Vision Lab,"{""en"":""This paper examines the revolutionary impact of deep learning on image recognition, highlighting key advantages such as automatic feature learning, hierarchical representations, and superior performance compared to traditional methods."",""zh"":""本文研究了深度学习对图像识别的革命性影响，强调了自动特征学习、层次化表示和相比传统方法的优越性能等关键优势。""}",deep_learning_vision.json
10,自然语言处理,natural language processing,自然语言处理技术如何改进人机交互？,How can natural language processing technology improve human-computer interaction?,论文研究了自然语言处理技术在人机交互中的应用，包括语音识别、文本理解和对话系统等，使计算机能够更好地理解和响应人类语言。,The paper studies the application of natural language processing technology in human-computer interaction, including speech recognition, text understanding, and dialogue systems, enabling computers to better understand and respond to human language.,Natural Language Processing for Enhanced Human-Computer Interaction,NLP Research Team,"{""en"":""This paper explores how natural language processing technologies can significantly enhance human-computer interaction through improved speech recognition, text understanding, and intelligent dialogue systems."",""zh"":""本文探讨了自然语言处理技术如何通过改进语音识别、文本理解和智能对话系统来显著增强人机交互。""}",nlp_hci.json
11,人工智能,artificial intelligence,人工智能在医疗领域有哪些应用前景？,What are the application prospects of artificial intelligence in the medical field?,论文探讨了人工智能在医疗领域的应用前景，包括疾病诊断、药物发现、个性化治疗和医疗影像分析等方面，展现了AI技术的巨大潜力。,The paper explores the application prospects of artificial intelligence in the medical field, including disease diagnosis, drug discovery, personalized treatment, and medical image analysis, demonstrating the great potential of AI technology.,Artificial Intelligence in Healthcare: Current Applications and Future Prospects,Medical AI Consortium,"{""en"":""This paper provides a comprehensive analysis of AI applications in healthcare, covering disease diagnosis, drug discovery, personalized medicine, and medical imaging, highlighting the transformative potential of AI in improving patient care."",""zh"":""本文全面分析了人工智能在医疗保健中的应用，涵盖疾病诊断、药物发现、个性化医疗和医学影像，强调了AI在改善患者护理方面的变革潜力。""}",ai_healthcare.json
12,云计算,cloud computing,云计算如何提高企业的IT效率？,How does cloud computing improve enterprise IT efficiency?,论文分析了云计算对企业IT效率的提升作用，包括资源弹性扩展、成本优化、运维简化和服务可靠性提高等方面的优势。,The paper analyzes how cloud computing improves enterprise IT efficiency, including advantages in resource elastic scaling, cost optimization, simplified operations, and improved service reliability.,Cloud Computing for Enterprise IT Efficiency Enhancement,Cloud Technology Research,"{""en"":""This paper examines how cloud computing technologies can significantly enhance enterprise IT efficiency through elastic resource scaling, cost optimization, simplified operations, and improved service reliability."",""zh"":""本文研究了云计算技术如何通过弹性资源扩展、成本优化、简化运维和提高服务可靠性来显著增强企业IT效率。""}",cloud_computing_efficiency.json
13,物联网,internet of things,物联网技术在智慧城市建设中的作用是什么？,What is the role of IoT technology in smart city construction?,论文研究了物联网技术在智慧城市建设中的关键作用，包括智能交通、环境监测、能源管理和公共安全等应用场景。,The paper studies the key role of IoT technology in smart city construction, including application scenarios such as intelligent transportation, environmental monitoring, energy management, and public safety.,IoT Technologies for Smart City Development,Smart City Research Institute,"{""en"":""This paper investigates the crucial role of Internet of Things technologies in smart city development, covering applications in intelligent transportation, environmental monitoring, energy management, and public safety systems."",""zh"":""本文研究了物联网技术在智慧城市发展中的关键作用，涵盖智能交通、环境监测、能源管理和公共安全系统等应用。""}",iot_smart_city.json
14,大数据,big data,大数据分析如何支持商业决策？,How does big data analysis support business decision-making?,论文探讨了大数据分析在商业决策中的支持作用，包括市场趋势分析、客户行为预测、风险评估和运营优化等方面。,The paper explores the supporting role of big data analysis in business decision-making, including market trend analysis, customer behavior prediction, risk assessment, and operational optimization.,Big Data Analytics for Business Decision Support,Business Intelligence Lab,"{""en"":""This paper explores how big data analytics can effectively support business decision-making through market trend analysis, customer behavior prediction, risk assessment, and operational optimization."",""zh"":""本文探讨了大数据分析如何通过市场趋势分析、客户行为预测、风险评估和运营优化来有效支持商业决策。""}",big_data_business.json
15,区块链,blockchain,区块链技术如何保证数据的安全性和透明性？,How does blockchain technology ensure data security and transparency?,论文分析了区块链技术在保证数据安全性和透明性方面的机制，包括分布式存储、加密算法、共识机制和不可篡改性等特点。,The paper analyzes the mechanisms of blockchain technology in ensuring data security and transparency, including distributed storage, encryption algorithms, consensus mechanisms, and immutability.,Blockchain Technology for Data Security and Transparency,Blockchain Research Center,"{""en"":""This paper analyzes how blockchain technology ensures data security and transparency through distributed storage, cryptographic algorithms, consensus mechanisms, and immutable record-keeping."",""zh"":""本文分析了区块链技术如何通过分布式存储、加密算法、共识机制和不可变记录保存来确保数据安全性和透明性。""}",blockchain_security.json
16,量子计算,quantum computing,量子计算对传统计算有哪些突破性优势？,What breakthrough advantages does quantum computing have over traditional computing?,论文研究了量子计算相对于传统计算的突破性优势，包括并行计算能力、特定问题的指数级加速和量子算法的独特性质。,The paper studies the breakthrough advantages of quantum computing over traditional computing, including parallel computing capabilities, exponential speedup for specific problems, and unique properties of quantum algorithms.,Quantum Computing: Breakthrough Advantages and Future Prospects,Quantum Research Institute,"{""en"":""This paper examines the revolutionary advantages of quantum computing over classical computing, including massive parallel processing capabilities, exponential speedup for certain problems, and unique quantum algorithmic properties."",""zh"":""本文研究了量子计算相对于经典计算的革命性优势，包括大规模并行处理能力、特定问题的指数级加速和独特的量子算法特性。""}",quantum_computing.json
17,边缘计算,edge computing,边缘计算如何降低网络延迟？,How does edge computing reduce network latency?,论文分析了边缘计算在降低网络延迟方面的作用机制，通过将计算资源部署在网络边缘，减少数据传输距离和处理时间。,The paper analyzes the mechanism of edge computing in reducing network latency by deploying computing resources at the network edge, reducing data transmission distance and processing time.,Edge Computing for Low-Latency Applications,Edge Computing Lab,"{""en"":""This paper analyzes how edge computing significantly reduces network latency by deploying computational resources closer to data sources and end users, minimizing data transmission distances and processing delays."",""zh"":""本文分析了边缘计算如何通过将计算资源部署在更接近数据源和终端用户的位置来显著降低网络延迟，最小化数据传输距离和处理延迟。""}",edge_computing.json
18,5G网络,5G network,5G网络技术如何推动产业数字化转型？,How does 5G network technology drive industrial digital transformation?,论文研究了5G网络技术对产业数字化转型的推动作用，包括高速连接、低延迟通信和大规模设备连接等技术特性。,The paper studies how 5G network technology drives industrial digital transformation, including technical characteristics such as high-speed connectivity, low-latency communication, and massive device connectivity.,5G Networks Enabling Industrial Digital Transformation,5G Research Consortium,"{""en"":""This paper investigates how 5G network technology serves as a catalyst for industrial digital transformation through high-speed connectivity, ultra-low latency communication, and massive IoT device connectivity."",""zh"":""本文研究了5G网络技术如何通过高速连接、超低延迟通信和大规模物联网设备连接作为产业数字化转型的催化剂。""}",5g_industrial.json
19,虚拟现实,virtual reality,虚拟现实技术在教育领域有哪些创新应用？,What innovative applications does virtual reality technology have in the field of education?,论文探讨了虚拟现实技术在教育领域的创新应用，包括沉浸式学习体验、虚拟实验室、历史场景重现和技能培训等方面。,The paper explores innovative applications of virtual reality technology in education, including immersive learning experiences, virtual laboratories, historical scene recreation, and skills training.,Virtual Reality in Education: Innovative Applications and Learning Enhancement,VR Education Research,"{""en"":""This paper explores innovative applications of virtual reality in education, including immersive learning experiences, virtual laboratories, historical recreations, and hands-on skills training programs."",""zh"":""本文探讨了虚拟现实在教育中的创新应用，包括沉浸式学习体验、虚拟实验室、历史重现和实践技能培训项目。""}",vr_education.json
20,增强现实,augmented reality,增强现实如何改变用户的交互体验？,How does augmented reality change user interaction experiences?,论文分析了增强现实技术对用户交互体验的改变，通过将虚拟信息叠加到现实世界中，创造了全新的交互模式和用户体验。,The paper analyzes how augmented reality technology changes user interaction experiences by overlaying virtual information onto the real world, creating new interaction modes and user experiences.,Augmented Reality: Transforming User Interaction Experiences,AR Interface Lab,"{""en"":""This paper analyzes how augmented reality technology transforms user interaction experiences by seamlessly blending virtual information with the real world, creating innovative interaction paradigms and enhanced user engagement."",""zh"":""本文分析了增强现实技术如何通过将虚拟信息与现实世界无缝融合来转变用户交互体验，创造创新的交互范式和增强的用户参与度。""}",ar_interaction.json
