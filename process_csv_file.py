import pandas as pd
import hashlib
import json
import csv

def process_csv_file():
    try:
        # 读取CSV文件，使用更严格的解析设置
        df = pd.read_csv('论文数据集_rag.csv', 
                        encoding='utf-8',
                        quoting=csv.QUOTE_ALL,
                        escapechar='\\',
                        on_bad_lines='warn')
        
        # 创建一个用于判断重复的字典
        unique_entries = {}
        unique_rows = []
        
        # 遍历DataFrame的每一行
        for index, row in df.iterrows():
            # 使用论文名称、作者和文件名称作为唯一性判断依据
            key = f"{row['论文名称']}_{row['作者']}_{row['文件名称']}"
            key_hash = hashlib.md5(key.encode()).hexdigest()
            
            if key_hash not in unique_entries:
                unique_entries[key_hash] = True
                unique_rows.append(row)
        
        # 创建新的DataFrame
        new_df = pd.DataFrame(unique_rows)
        
        # 重置索引并重新生成序号列
        new_df.reset_index(drop=True, inplace=True)
        new_df['序号'] = range(1, len(new_df) + 1)
        
        # 保存到新文件，确保正确处理引号和逗号
        new_df.to_csv('论文数据集_rag_processed.csv', index=False, quoting=1)
        
        print(f"原始行数: {len(df)}")
        print(f"处理后行数: {len(new_df)}")
        print(f"移除重复行数: {len(df) - len(new_df)}")
    
    except Exception as e:
        print(f"处理文件时出错: {str(e)}")

if __name__ == "__main__":
    process_csv_file()
