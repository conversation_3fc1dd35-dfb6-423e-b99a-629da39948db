import os
import json
from collections import OrderedDict

def process_json_files():
    folder_path = os.path.dirname(os.path.abspath(__file__))
    json_files = [f for f in os.listdir(folder_path) if f.endswith('.json')]
    
    # Store unique entries
    unique_entries = {}
    
    # First pass: collect all unique entries
    for filename in json_files:
        file_path = os.path.join(folder_path, filename)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                content_hash = str(hash(json.dumps(data, sort_keys=True)))
                if content_hash not in unique_entries:
                    unique_entries[content_hash] = (filename, data)
        except Exception as e:
            print(f"Error processing {filename}: {str(e)}")
    
    # Second pass: rename and rewrite files
    for i, (_, (old_filename, data)) in enumerate(unique_entries.items(), 1):
        new_filename = f"{str(i).zfill(6)}.json"
        new_path = os.path.join(folder_path, new_filename)
        
        try:
            with open(new_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
            
            # If the new filename is different from the old one, delete the old file
            old_path = os.path.join(folder_path, old_filename)
            if old_path != new_path and os.path.exists(old_path):
                os.remove(old_path)
                
        except Exception as e:
            print(f"Error writing {new_filename}: {str(e)}")
    
    print(f"Processed {len(json_files)} files")
    print(f"Kept {len(unique_entries)} unique entries")
    print(f"Removed {len(json_files) - len(unique_entries)} duplicates")

if __name__ == "__main__":
    process_json_files()
