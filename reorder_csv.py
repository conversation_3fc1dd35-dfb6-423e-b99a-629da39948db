import csv

def reorder_csv():
    try:
        # 读取原始文件内容
        with open('论文数据集_rag.csv', 'r', encoding='utf-8') as f:
            # 使用csv模块读取，处理引号内的逗号
            reader = csv.reader(f, quotechar='"', doublequote=True, quoting=csv.QUOTE_ALL)
            # 获取所有行
            all_rows = list(reader)
            
        if not all_rows:
            print("文件为空")
            return
            
        # 获取标题行
        header = all_rows[0]
        
        # 处理数据行
        data_rows = [row for row in all_rows[1:] if row and any(row)]  # 跳过空行
        
        # 重新写入文件
        with open('论文数据集_rag_processed.csv', 'w', encoding='utf-8', newline='') as f:
            writer = csv.writer(f, quotechar='"', quoting=csv.QUOTE_ALL)
            # 写入标题行
            writer.writerow(header)
            # 写入数据行，更新序号
            for i, row in enumerate(data_rows, 1):
                row[0] = str(i)  # 更新序号
                writer.writerow(row)
        
        print(f"总行数: {len(data_rows)}")
        print("序号重排完成")
    
    except Exception as e:
        print(f"处理文件时出错: {str(e)}")

if __name__ == "__main__":
    reorder_csv()
